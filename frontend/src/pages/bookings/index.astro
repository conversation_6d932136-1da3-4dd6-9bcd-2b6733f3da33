---
import BaseLayout from '../../layouts/BaseLayout.astro';

// Check if user is authenticated
const authCookie = Astro.cookies.get('pb_auth');
if (!authCookie) {
  return Astro.redirect('/auth/login');
}
---

<BaseLayout
  title="My Bookings - Trodoo"
  description="Manage your venue bookings, view booking history, and track upcoming reservations."
>
  <main class="min-h-screen bg-gradient-to-br from-slate-50 to-white">
    <!-- App Wrapper Container -->
    <div id="app-wrapper-container">
      <!-- Header -->
      <div class="bg-white border-b border-slate-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="py-8">
            <div class="flex items-center justify-between">
              <div>
                <nav class="flex items-center space-x-2 text-sm text-slate-500 mb-2">
                  <a href="/dashboard" class="hover:text-slate-700 transition-colors">Dashboard</a>
                  <span>/</span>
                  <span class="text-slate-900 font-medium">My Bookings</span>
                </nav>
                <h1 class="text-3xl font-bold text-slate-900">My Bookings</h1>
                <p class="mt-2 text-slate-600">
                  Manage your venue reservations and booking history.
                </p>
              </div>
              <div class="flex items-center space-x-3">
                <a
                  href="/venues"
                  class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                >
                  Browse Venues
                </a>


                <!-- Profile Dropdown -->
                <div id="profile-dropdown-container"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Filter Tabs -->
        <div class="mb-8">
          <div class="border-b border-slate-200">
            <nav class="-mb-px flex space-x-8" id="booking-tabs">
              <button
                class="tab-button active border-transparent text-primary-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                data-tab="all"
              >
                All Bookings
              </button>
              <button
                class="tab-button border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                data-tab="upcoming"
              >
                Upcoming
              </button>
              <button
                class="tab-button border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                data-tab="past"
              >
                Past
              </button>
              <button
                class="tab-button border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                data-tab="cancelled"
              >
                Cancelled
              </button>
            </nav>
          </div>
        </div>

        <!-- Bookings Container -->
        <div id="bookings-container">
          <!-- Loading State -->
          <div id="loading-state" class="space-y-6">
            <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6 animate-pulse">
              <div class="flex items-center space-x-4">
                <div class="w-24 h-24 bg-slate-200 rounded-lg"></div>
                <div class="flex-1 space-y-2">
                  <div class="h-4 bg-slate-200 rounded w-1/3"></div>
                  <div class="h-3 bg-slate-200 rounded w-1/2"></div>
                  <div class="h-3 bg-slate-200 rounded w-1/4"></div>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6 animate-pulse">
              <div class="flex items-center space-x-4">
                <div class="w-24 h-24 bg-slate-200 rounded-lg"></div>
                <div class="flex-1 space-y-2">
                  <div class="h-4 bg-slate-200 rounded w-1/3"></div>
                  <div class="h-3 bg-slate-200 rounded w-1/2"></div>
                  <div class="h-3 bg-slate-200 rounded w-1/4"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div id="empty-state" class="hidden text-center py-12">
            <div class="w-24 h-24 text-slate-400 mx-auto mb-4">
              <svg class="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">No bookings found</h3>
            <p class="text-slate-600 mb-6">You haven't made any bookings yet. Start by browsing our venues.</p>
            <a
              href="/venues"
              class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 transition-colors"
            >
              Browse Venues
            </a>
          </div>
        </div>
      </div>
    </div>


  </main>
</BaseLayout>

<!-- React Components Integration -->
<script>
  import BookingList from '../../components/dashboard/BookingList.tsx';

  import AppWrapper from '../../components/common/AppWrapper.tsx';
  import ProfileDropdown from '../../components/common/ProfileDropdown.tsx';
  import { createRoot } from 'react-dom/client';
  import React from 'react';
  import { getUserBookings } from '../../lib/pocketbase.ts';
  import { userStore, initializeAuth, authLoadingStore } from '../../lib/state.ts';

  // Helper function to wait for authentication to complete
  function waitForAuth(): Promise<void> {
    return new Promise((resolve) => {
      const checkAuth = () => {
        if (!authLoadingStore.get()) {
          resolve();
        } else {
          setTimeout(checkAuth, 100);
        }
      };
      checkAuth();
    });
  }

  // Bookings page initialization
  document.addEventListener('DOMContentLoaded', async () => {
    // Initialize authentication state first
    initializeAuth();

    // Wait for auth to be ready
    await waitForAuth();

    // Mount AppWrapper component to wrap the entire content
    const appWrapperContainer = document.getElementById('app-wrapper-container');
    if (appWrapperContainer) {
      const originalContent = appWrapperContainer.innerHTML;
      const root = createRoot(appWrapperContainer);
      root.render(React.createElement(AppWrapper, {
        children: React.createElement('div', { dangerouslySetInnerHTML: { __html: originalContent } })
      }));

      // Wait for React to render the new DOM, then mount ProfileDropdown
      setTimeout(() => {
        const profileDropdownContainer = document.getElementById('profile-dropdown-container');
        if (profileDropdownContainer) {
          const profileRoot = createRoot(profileDropdownContainer);
          profileRoot.render(React.createElement(ProfileDropdown));
        }
      }, 100);
    }



    // Get current user from state
    const user = userStore.get();
    if (!user) {
      console.error('No authenticated user found');
      globalThis.location.href = '/auth/login';
      return;
    }

    // Initialize tab functionality
    initializeTabs();

    // Load bookings data
    await loadBookingsData();
  });

  // Tab functionality
  function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const target = e.target as HTMLButtonElement;
        const tabType = target.dataset.tab;

        // Only proceed if tabType exists
        if (!tabType) return;

        // Update active tab
        tabButtons.forEach(btn => {
          btn.classList.remove('active', 'border-primary-600', 'text-primary-600');
          btn.classList.add('border-transparent', 'text-slate-500');
        });

        target.classList.add('active', 'border-primary-600', 'text-primary-600');
        // @ts-ignore - TypeScript false positive about string literals
        target.classList.remove('border-transparent', 'text-slate-500');

        // Filter bookings
        filterBookings(tabType as string);
      });
    });
  }

  let allBookings: any[] = [];

  // Load bookings data
  async function loadBookingsData() {
    try {
      const user = userStore.get();
      if (!user) return;

      // Load bookings for both renter and owner roles
      const [renterBookings, ownerBookings] = await Promise.all([
        getUserBookings(user.id || undefined, 1, 50, 'renter'),
        getUserBookings(user.id || undefined, 1, 50, 'owner')
      ]);

      // Combine bookings and remove duplicates based on booking ID
      const allBookingsMap = new Map();

      // Add renter bookings
      if (renterBookings.success) {
        renterBookings.bookings.forEach(booking => {
          allBookingsMap.set(booking.id, booking);
        });
      }

      // Add owner bookings (will overwrite if same ID exists)
      if (ownerBookings.success) {
        ownerBookings.bookings.forEach(booking => {
          allBookingsMap.set(booking.id, booking);
        });
      }

      allBookings = Array.from(allBookingsMap.values());

      // Sort by creation date (newest first)
      allBookings.sort((a, b) => new Date(b.created).getTime() - new Date(a.created).getTime());

      // Hide loading state
      const loadingState = document.getElementById('loading-state');
      if (loadingState) loadingState.style.display = 'none';

      // Show bookings or empty state
      if (allBookings.length > 0) {
        renderBookings(allBookings);
      } else {
        showEmptyState();
      }

    } catch (error) {
      console.error('Failed to load bookings:', error);
      showErrorState();
    }
  }

  // Filter bookings by type
  function filterBookings(type: string) {
    let filteredBookings = allBookings;
    const now = new Date();

    switch (type) {
      case 'upcoming':
        filteredBookings = allBookings.filter(booking => 
          new Date(booking.start_date) > now && !['cancelled', 'rejected'].includes(booking.status)
        );
        break;
      case 'past':
        filteredBookings = allBookings.filter(booking => 
          new Date(booking.end_date) < now || booking.status === 'completed'
        );
        break;
      case 'cancelled':
        filteredBookings = allBookings.filter(booking => 
          ['cancelled', 'rejected'].includes(booking.status)
        );
        break;
      default:
        filteredBookings = allBookings;
    }

    if (filteredBookings.length > 0) {
      renderBookings(filteredBookings);
    } else {
      showEmptyState();
    }
  }

  // Render bookings list
  function renderBookings(bookings: any[]) {
    const container = document.getElementById('bookings-container');
    if (!container) return;

    // Clear container
    container.innerHTML = '';

    // Mount BookingList component
    const root = createRoot(container);

    const handleBookingAction = (bookingId: string, action: string) => {
      switch (action) {
        case 'view':
        case 'message':
          globalThis.location.href = `/bookings/${bookingId}`;
          break;
        case 'pay':
          globalThis.location.href = `/bookings/${bookingId}?action=pay`;
          break;
        default:
          globalThis.location.href = `/bookings/${bookingId}`;
      }
    };

    // Determine primary user role
    const user = userStore.get();
    const primaryRole = bookings.some(b => b.venue.owner_id === user?.id) ? 'owner' : 'renter';

    root.render(
      React.createElement(BookingList, {
        bookings: bookings,
        userRole: primaryRole,
        onBookingAction: handleBookingAction,
        isLoading: false
      })
    );
  }

  // Show empty state
  function showEmptyState() {
    const container = document.getElementById('bookings-container');
    const emptyState = document.getElementById('empty-state');
    if (container && emptyState) {
      container.innerHTML = '';
      container.appendChild(emptyState.cloneNode(true));
      container.lastElementChild?.classList.remove('hidden');
    }
  }

  // Show error state
  function showErrorState() {
    const container = document.getElementById('bookings-container');
    if (container) {
      container.innerHTML = `
        <div class="text-center py-12">
          <div class="w-24 h-24 text-red-500 mx-auto mb-4">
            <svg class="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">Failed to load bookings</h3>
          <p class="text-slate-600 mb-6">There was an error loading your bookings. Please try again.</p>
          <button onclick="globalThis.location.reload()" class="inline-flex items-center px-6 py-3 border border-slate-300 text-base font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-colors">
            Try Again
          </button>
        </div>
      `;
    }
  }
</script>

<style>
  /* Custom styles for bookings page */
  .shadow-card {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }

  /* Tab button active state */
  .tab-button.active {
    border-color: var(--color-primary-600);
    color: var(--color-primary-600);
  }

  /* Loading animation */
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .max-w-7xl {
      padding-left: 1rem;
      padding-right: 1rem;
    }
    
    #booking-tabs {
      flex-wrap: wrap;
      gap: 1rem;
    }
    
    .tab-button {
      flex: 1;
      text-align: center;
    }
  }
</style>
