---
import BaseLayout from '../../layouts/BaseLayout.astro';
import Header from '../../components/core/Header.astro';


// Check if user is authenticated
const authCookie = Astro.cookies.get('pb_auth');
if (!authCookie) {
  return Astro.redirect('/auth/login');
}
---

<BaseLayout
  title="Dashboard - Trodoo"
  description="Manage your bookings, venues, and account settings."
>
<Header />
  <main class="min-h-screen bg-gradient-to-br from-slate-50 to-white">
    <!-- App Wrapper Container -->
    <div id="app-wrapper-container">
      <!-- Dashboard Header -->
      <div class="bg-white border-b border-slate-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-8">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-slate-900">Dashboard</h1>
              <p class="mt-2 text-slate-600">
                Welcome back! Here's what's happening with your account.
              </p>
            </div>
            <div class="flex items-center space-x-3">
              <a
                href="/venues/new"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
              >
                List Your Venue
              </a>
              <a
                href="/venues"
                class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
              >
                Browse Venues
              </a>

              <!-- Profile Dropdown -->
              <div id="profile-dropdown-container"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Review Prompts -->
      <div id="review-prompts-container" class="mb-6"></div>

      <!-- Welcome Message (for new users) -->
      <div id="welcome-banner" class="hidden mb-8 bg-primary-50 border border-primary-200 rounded-lg p-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-primary-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-primary-800">
              Welcome to Trodoo!
            </h3>
            <div class="mt-2 text-sm text-primary-700">
              <p>Your account has been created successfully. Start by browsing venues or listing your own space.</p>
            </div>
            <div class="mt-4">
              <div class="-mx-2 -my-1.5 flex">
                <button type="button" class="bg-primary-50 px-2 py-1.5 rounded-md text-sm font-medium text-primary-800 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-primary-50 focus:ring-primary-600" onclick="this.parentElement.parentElement.parentElement.parentElement.style.display='none'">
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Role-Based Dashboard Container -->
      <div id="unified-dashboard-container">
        <!-- UnifiedDashboard component will be mounted here -->
        <div id="dashboard-loading" class="text-center py-12">
          <div class="inline-flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
            <span class="text-slate-600 font-medium">Loading your dashboard...</span>
          </div>
          <p class="text-sm text-slate-500 mt-2">Detecting your roles and preparing your personalized experience</p>
        </div>

        <!-- Error state (hidden by default) -->
        <div id="dashboard-error" class="hidden text-center py-12">
          <div class="max-w-md mx-auto">
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
              <div class="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-red-900 mb-2">Dashboard Loading Error</h3>
              <p class="text-sm text-red-700 mb-4">We encountered an issue loading your dashboard. Please try refreshing the page.</p>
              <button
                onclick="window.location.reload()"
                class="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors"
              >
                Refresh Page
              </button>
            </div>
          </div>
        </div>
      </div>


    </div>
    </div> <!-- End app-wrapper-container -->
  </main>


</BaseLayout>

<!-- React Components Integration -->
<script>
  import UnifiedDashboard from '../../components/dashboard/UnifiedDashboard.tsx';
  import ReviewPromptBanner from '../../components/dashboard/ReviewPromptBanner.tsx';
  import AppWrapper from '../../components/common/AppWrapper.tsx';
  import ProfileDropdown from '../../components/common/ProfileDropdown.tsx';
  import { createRoot } from 'react-dom/client';
  import React from 'react';
  import { userStore, initializeAuth, authLoadingStore } from '../../lib/state.ts';

  // Helper function to wait for authentication to complete
  function waitForAuth(): Promise<void> {
    return new Promise((resolve) => {
      const checkAuth = () => {
        if (!authLoadingStore.get()) {
          resolve();
        } else {
          setTimeout(checkAuth, 100);
        }
      };
      checkAuth();
    });
  }

  // Dashboard initialization
  document.addEventListener('DOMContentLoaded', async () => {
    // Initialize authentication state first
    initializeAuth();

    // Wait for auth to be ready
    await waitForAuth();

    // Mount UnifiedDashboard component with enhanced error handling
    const unifiedDashboardContainer = document.getElementById('unified-dashboard-container');
    const loadingElement = document.getElementById('dashboard-loading');
    const errorElement = document.getElementById('dashboard-error');

    if (unifiedDashboardContainer) {
      try {
        console.log('Dashboard - Mounting UnifiedDashboard component');

        // Create root and render component
        const dashboardRoot = createRoot(unifiedDashboardContainer);
        dashboardRoot.render(React.createElement(UnifiedDashboard));

        // Hide loading state after a reasonable timeout if component doesn't respond
        setTimeout(() => {
          if (loadingElement && loadingElement.style.display !== 'none') {
            console.warn('Dashboard - Component took too long to load, hiding loading state');
            loadingElement.style.display = 'none';
          }
        }, 12000); // Slightly longer than component timeout

      } catch (err) {
        console.error('Dashboard - Failed to mount UnifiedDashboard:', err);
        if (loadingElement) loadingElement.style.display = 'none';
        if (errorElement) errorElement.style.display = 'block';
      }
    } else {
      console.error('Dashboard - unified-dashboard-container not found');
      if (errorElement) errorElement.style.display = 'block';
    }

    // Mount AppWrapper component to wrap the entire dashboard content
    const appWrapperContainer = document.getElementById('app-wrapper-container');
    if (appWrapperContainer) {
      const originalContent = appWrapperContainer.innerHTML;
      const root = createRoot(appWrapperContainer);
      root.render(React.createElement(AppWrapper, {
        children: React.createElement('div', { dangerouslySetInnerHTML: { __html: originalContent } })
      }));

      // Wait for React to render the new DOM, then mount ProfileDropdown
      setTimeout(() => {
        const profileDropdownContainer = document.getElementById('profile-dropdown-container');
        if (profileDropdownContainer) {
          const profileRoot = createRoot(profileDropdownContainer);
          profileRoot.render(React.createElement(ProfileDropdown));
        }
      }, 100);
    }



    // Mount ReviewPromptBanner component
    const reviewPromptsContainer = document.getElementById('review-prompts-container');
    if (reviewPromptsContainer) {
      const root = createRoot(reviewPromptsContainer);
      root.render(React.createElement(ReviewPromptBanner));
    }

    // Check for welcome parameter
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('welcome') === 'true') {
      const welcomeBanner = document.getElementById('welcome-banner');
      if (welcomeBanner) {
        welcomeBanner.classList.remove('hidden');
      }
    }

    // Get current user from state
    const user = userStore.get();
    if (!user) {
      console.error('No authenticated user found');
      globalThis.location.href = '/auth/login';
      return;
    }

    console.log('Dashboard - User authenticated:', user.id, 'Email:', user.email, 'Roles:', user.roles, 'Full user object:', user);
  });
</script>


