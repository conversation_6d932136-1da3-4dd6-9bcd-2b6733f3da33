---
import BaseLayout from '../../layouts/BaseLayout.astro';
import Header from '../../components/core/Header.astro';


// Check if user is authenticated
const authCookie = Astro.cookies.get('pb_auth');
if (!authCookie) {
  return Astro.redirect('/auth/login');
}
---

<BaseLayout
  title="Dashboard - Trodoo"
  description="Manage your bookings, venues, and account settings."
>
<Header />
  <main class="min-h-screen bg-gradient-to-br from-slate-50 to-white">
    <!-- App Wrapper Container -->
    <div id="app-wrapper-container">
      <!-- Dashboard Header -->
      <div class="bg-white border-b border-slate-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-8">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-slate-900">Dashboard</h1>
              <p class="mt-2 text-slate-600">
                Welcome back! Here's what's happening with your account.
              </p>
            </div>
            <div class="flex items-center space-x-3">
              <a
                href="/venues/new"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
              >
                List Your Venue
              </a>
              <a
                href="/venues"
                class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
              >
                Browse Venues
              </a>

              <!-- Profile Dropdown -->
              <div id="profile-dropdown-container"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Review Prompts -->
      <div id="review-prompts-container" class="mb-6"></div>

      <!-- Welcome Message (for new users) -->
      <div id="welcome-banner" class="hidden mb-8 bg-primary-50 border border-primary-200 rounded-lg p-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-primary-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-primary-800">
              Welcome to Trodoo!
            </h3>
            <div class="mt-2 text-sm text-primary-700">
              <p>Your account has been created successfully. Start by browsing venues or listing your own space.</p>
            </div>
            <div class="mt-4">
              <div class="-mx-2 -my-1.5 flex">
                <button type="button" class="bg-primary-50 px-2 py-1.5 rounded-md text-sm font-medium text-primary-800 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-primary-50 focus:ring-primary-600" onclick="this.parentElement.parentElement.parentElement.parentElement.style.display='none'">
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Simple loading message while redirecting -->
      <div class="text-center py-12">
        <div class="inline-flex items-center space-x-3">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
          <span class="text-slate-600 font-medium">Redirecting to your dashboard...</span>
        </div>
        <p class="text-sm text-slate-500 mt-2">Detecting your role and taking you to the right place</p>
      </div>


    </div>
    </div> <!-- End app-wrapper-container -->
  </main>


</BaseLayout>

<!-- React Components Integration -->
<script>
  import { userStore, initializeAuth, authLoadingStore } from '../../lib/state.ts';

  // Helper function to wait for authentication to complete
  function waitForAuth(): Promise<void> {
    return new Promise((resolve) => {
      const checkAuth = () => {
        if (!authLoadingStore.get()) {
          resolve();
        } else {
          setTimeout(checkAuth, 100);
        }
      };
      checkAuth();
    });
  }

  // Dashboard initialization
  document.addEventListener('DOMContentLoaded', async () => {
    // Initialize authentication state first
    initializeAuth();

    // Wait for auth to be ready
    await waitForAuth();

    // No need to mount complex components - just redirect based on role

    // Get current user from state
    const user = userStore.get();
    if (!user) {
      console.error('No authenticated user found');
      globalThis.location.href = '/auth/login';
      return;
    }

    console.log('Dashboard - User authenticated:', user.id, 'Email:', user.email, 'Roles:', user.roles, 'Full user object:', user);

    // Redirect based on primary role (first role in array)
    const userRoles = user.roles || ['renter'];
    const primaryRole = userRoles[0];

    console.log('Dashboard - Primary role detected:', primaryRole, 'from roles:', userRoles);

    // Redirect to appropriate page based on primary role
    switch (primaryRole) {
      case 'admin':
        console.log('Dashboard - Redirecting to admin dashboard');
        globalThis.location.href = '/admin';
        return;
      case 'owner':
        console.log('Dashboard - Redirecting to venue management');
        globalThis.location.href = '/dashboard/my-venues';
        return;
      case 'renter':
      default:
        console.log('Dashboard - Redirecting to bookings');
        globalThis.location.href = '/bookings';
        return;
    }
  });
</script>


