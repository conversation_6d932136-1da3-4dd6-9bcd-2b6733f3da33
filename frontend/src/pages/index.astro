---
import BaseLayout from '../layouts/BaseLayout.astro';
import Header from '../components/core/Header.astro';
import GridMotion from '../Backgrounds/GridMotion/GridMotion';
import GradientText from '../TextAnimations/GradientText/GradientText';
import TrueFocus from '../TextAnimations/TrueFocus/TrueFocus';
// SpotlightTiltCard component available but not currently used
import HeroSearch from '../components/HeroSearch/HeroSearch';
import StarBorder from '../Animations/StarBorder/StarBorder';
import FeaturesSection from '../components/FeatureCardList/feature-cards-section.tsx';
import Footer from "../components/Footer/Footer.tsx"
import InfoPopover from "../components/InfoPopover/InfoPopover.tsx"
---

<BaseLayout
  title="Trodoo - Find Your Perfect Venue"
  description="Discover and book amazing venues for your events, meetings, and special occasions. Direct connection between renters and property owners."
>
  <Header />
  <main>
    <!-- Hero Section with GridMotion Background -->
    <section class="relative min-h-screen overflow-hidden">
      <!-- GridMotion Background -->
      <div class="absolute inset-0 z-0">
        <GridMotion
          client:only="react"
          gradientColor="rgba(5, 150, 105, 0.1)"
          items={[
            "🏢 Corporate Events",
            "💍 Weddings",
            "🎉 Celebrations",
            "📊 Meetings",
            "🎨 Workshops",
            "🎭 Performances",
            "🍽️ Dining",
            "🏃 Fitness",
            "📚 Training",
            "🎵 Music",
            "🖼️ Exhibitions",
            "🎪 Entertainment",
            "💼 Business",
            "🌟 Special Events",
            "🏛️ Conferences",
            "🎬 Productions",
            "� Awards",
            "🎓 Education",
            "🤝 Networking",
            "🎨 Creative",
            "🏠 Private",
            "🌿 Outdoor",
            "🏖️ Beachfront",
            "🏔️ Mountain",
            "🌆 Urban",
            "🌸 Garden",
            "🏰 Historic",
            "🔬 Tech"
          ]}
        />
      </div>

      <!-- Gradient Overlay for Better Text Readability -->
      <div
        class="absolute inset-0 z-10"
        style="background: linear-gradient(135deg, rgba(5, 150, 105, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%);"
      ></div>

      <!-- Hero Content -->
      <div class="relative z-20 flex items-center justify-center min-h-screen">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div class="text-center">
            <!-- Hero Headline - Following design system H1 specs -->
            <GradientText
              client:only="react"
              className="mb-6 text-6xl sm:text-8xl md:text-9xl font-extrabold leading-tight text-white font-anton"
              colors={["#FFFFFF", "#FBBF24", "#FFFFFF"]}>
              Find Your Perfect
              <span class="text-yellow-400 block mt-2">Venue</span>
            </GradientText>
            <InfoPopover
              client:load
              content={
                "Connect directly with property owners. Book amazing spaces for events, meetings, and special occasions. Experience seamless venue discovery with our modern platform."
              }
            />
            <!-- Hero Description - Following design system body large specs -->
            <HeroSearch client:load />

            <!-- Trust Indicators -->
            <TrueFocus
              client:load
              sentence="Trusted&nbsp;by&nbsp;1,000+&nbsp;users+&nbsp; Secure&nbsp;&&nbsp;Protected+&nbsp; Instant&nbsp;Booking"
              className="text-lg mt-12 flex flex-col sm:flex-row items-center justify-center gap-1 opacity-90 text-white font-semibold"
              borderColor="var(--color-secondary)"
              glowColor="var(--color-secondary-light)"
              blurAmount={1}
              pauseBetweenAnimations={1.5}
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
     <FeaturesSection client:visible />

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-primary-600 to-primary-500">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-center">
        <StarBorder client:visible color="#FBBF24" speed="4s" thickness={2}>
          <div class="bg-white/10 backdrop-blur-md rounded-2xl p-8 sm:p-12 text-center shadow-lg">
            <h2 class="text-4xl font-bold text-white mb-4">
              Got Property to Rent?
            </h2>
            <p class="text-lg text-white/90 mb-8 max-w-2xl mx-auto">
              Join thousands of owners who trust Trodoo for exposure.
            </p>
            <a
              href="/auth/register"
              class="inline-block bg-secondary-500 text-neutral-black font-bold text-lg px-8 py-3 rounded-full hover:bg-secondary-400 transition-colors duration-300 transform hover:scale-105 shadow-md"
            >
              Sign Up Today
            </a>
          </div>
        </StarBorder>
      </div>
    </section>
  </main>
  
  <Footer client:visible/>
  

</BaseLayout>
