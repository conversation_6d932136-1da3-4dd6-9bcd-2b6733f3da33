import { useState } from 'react';
import { ChevronDown, User, Building, Shield } from 'lucide-react';
import type { UserRole } from '../../lib/roleUtils.ts';

interface RoleSwitcherProps {
  availableRoles: UserRole[];
  currentRole: UserRole;
  onRoleChange: (role: UserRole) => void;
  className?: string;
}

const roleConfig = {
  renter: {
    label: 'Renter',
    icon: User,
    color: 'blue',
    description: 'Book venues for events'
  },
  owner: {
    label: 'Venue Owner',
    icon: Building,
    color: 'green',
    description: 'Manage your venues'
  },
  admin: {
    label: 'Administrator',
    icon: Shield,
    color: 'purple',
    description: 'System administration'
  }
};

export default function RoleSwitcher({ 
  availableRoles, 
  currentRole, 
  onRoleChange, 
  className = '' 
}: RoleSwitcherProps) {
  const [isOpen, setIsOpen] = useState(false);

  if (availableRoles.length <= 1) {
    return null; // Don't show switcher if user only has one role
  }

  const currentConfig = roleConfig[currentRole];
  const CurrentIcon = currentConfig.icon;

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 bg-white border border-slate-200 rounded-lg hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <div className={`p-1.5 rounded-md bg-${currentConfig.color}-100`}>
          <CurrentIcon className={`w-4 h-4 text-${currentConfig.color}-600`} />
        </div>
        <div className="text-left">
          <div className="text-sm font-medium text-slate-900">
            {currentConfig.label}
          </div>
          <div className="text-xs text-slate-500">
            {currentConfig.description}
          </div>
        </div>
        <ChevronDown 
          className={`w-4 h-4 text-slate-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-2 w-full bg-white border border-slate-200 rounded-lg shadow-lg z-20 overflow-hidden">
            {availableRoles.map((role) => {
              const config = roleConfig[role];
              const Icon = config.icon;
              const isSelected = role === currentRole;
              
              return (
                <button
                  key={role}
                  type="button"
                  onClick={() => {
                    onRoleChange(role);
                    setIsOpen(false);
                  }}
                  className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-slate-50 transition-colors ${
                    isSelected ? 'bg-primary-50 border-r-2 border-primary-500' : ''
                  }`}
                >
                  <div className={`p-1.5 rounded-md bg-${config.color}-100`}>
                    <Icon className={`w-4 h-4 text-${config.color}-600`} />
                  </div>
                  <div className="flex-1">
                    <div className={`text-sm font-medium ${
                      isSelected ? 'text-primary-900' : 'text-slate-900'
                    }`}>
                      {config.label}
                    </div>
                    <div className={`text-xs ${
                      isSelected ? 'text-primary-600' : 'text-slate-500'
                    }`}>
                      {config.description}
                    </div>
                  </div>
                  {isSelected && (
                    <div className="w-2 h-2 bg-primary-500 rounded-full" />
                  )}
                </button>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
}

// Alternative compact version for mobile/small spaces
export function CompactRoleSwitcher({ 
  availableRoles, 
  currentRole, 
  onRoleChange, 
  className = '' 
}: RoleSwitcherProps) {
  const [isOpen, setIsOpen] = useState(false);

  if (availableRoles.length <= 1) {
    return null;
  }

  const currentConfig = roleConfig[currentRole];
  const CurrentIcon = currentConfig.icon;

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 px-3 py-2 bg-white border border-slate-200 rounded-lg hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
        aria-expanded={isOpen}
        aria-haspopup="true"
        title={`Switch from ${currentConfig.label}`}
      >
        <div className={`p-1 rounded bg-${currentConfig.color}-100`}>
          <CurrentIcon className={`w-3 h-3 text-${currentConfig.color}-600`} />
        </div>
        <span className="text-xs font-medium text-slate-700">
          {currentConfig.label}
        </span>
        <ChevronDown 
          className={`w-3 h-3 text-slate-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          <div className="absolute top-full left-0 mt-1 bg-white border border-slate-200 rounded-lg shadow-lg z-20 overflow-hidden min-w-max">
            {availableRoles.map((role) => {
              const config = roleConfig[role];
              const Icon = config.icon;
              const isSelected = role === currentRole;
              
              return (
                <button
                  key={role}
                  type="button"
                  onClick={() => {
                    onRoleChange(role);
                    setIsOpen(false);
                  }}
                  className={`w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-slate-50 transition-colors ${
                    isSelected ? 'bg-primary-50' : ''
                  }`}
                >
                  <div className={`p-1 rounded bg-${config.color}-100`}>
                    <Icon className={`w-3 h-3 text-${config.color}-600`} />
                  </div>
                  <span className={`text-xs font-medium whitespace-nowrap ${
                    isSelected ? 'text-primary-900' : 'text-slate-900'
                  }`}>
                    {config.label}
                  </span>
                  {isSelected && (
                    <div className="w-1.5 h-1.5 bg-primary-500 rounded-full" />
                  )}
                </button>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
}

// Hook for managing role switching state
export function useRoleSwitcher(initialRole: UserRole) {
  const [currentRole, setCurrentRole] = useState<UserRole>(initialRole);

  const switchRole = (newRole: UserRole) => {
    setCurrentRole(newRole);
    
    // Store preference in localStorage
    try {
      localStorage.setItem('trodoo_preferred_role', newRole);
    } catch (error) {
      console.warn('Failed to save role preference:', error);
    }
  };

  // Get preferred role from localStorage
  const getPreferredRole = (availableRoles: UserRole[]): UserRole => {
    try {
      const stored = localStorage.getItem('trodoo_preferred_role') as UserRole;
      if (stored && availableRoles.includes(stored)) {
        return stored;
      }
    } catch (error) {
      console.warn('Failed to load role preference:', error);
    }

    // Default to first available role (respects the order from user.roles array)
    return availableRoles[0] || 'renter';
  };

  return {
    currentRole,
    switchRole,
    getPreferredRole
  };
}
