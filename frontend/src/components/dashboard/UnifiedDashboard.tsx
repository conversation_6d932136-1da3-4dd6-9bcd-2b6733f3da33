import { useState, useEffect } from 'react';
import { useStore } from '@nanostores/react';
import { userStore, userRoleInfoStore, roleDetectionLoadingStore } from '../../lib/state.ts';
import { detectUserRoles, getDashboardFeatures } from '../../lib/roleUtils.ts';
import RenterDashboard from './RenterDashboard.tsx';
import OwnerDashboard from './OwnerDashboard.tsx';
import AdminDashboard from '../admin/AdminDashboard.tsx';
import RoleSwitcher, { useRoleSwitcher } from './RoleSwitcher.tsx';
import type { UserRole, UserRoleInfo } from '../../lib/roleUtils.ts';

// Extended role type to include combined view
type DashboardView = UserRole | 'combined';

interface UnifiedDashboardProps {
  className?: string;
}

export default function UnifiedDashboard({ className = '' }: UnifiedDashboardProps) {
  const user = useStore(userStore);
  const roleInfo = useStore(userRoleInfoStore);
  const roleDetectionLoading = useStore(roleDetectionLoadingStore);
  const [isInitializing, setIsInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Debug logging for component mount
  useEffect(() => {
    console.log('UnifiedDashboard - Component mounted with initial state:', {
      hasUser: !!user,
      hasRoleInfo: !!roleInfo,
      roleDetectionLoading,
      isInitializing,
      userEmail: user?.email,
      userRoles: user?.roles
    });
  }, []);

  // Hide the loading element when component is ready
  useEffect(() => {
    if (!isInitializing && !roleDetectionLoading) {
      const loadingElement = document.getElementById('dashboard-loading');
      if (loadingElement) {
        loadingElement.style.display = 'none';
      }
    }
  }, [isInitializing, roleDetectionLoading]);

  // Show error state when there's an error
  useEffect(() => {
    if (error) {
      const loadingElement = document.getElementById('dashboard-loading');
      const errorElement = document.getElementById('dashboard-error');
      if (loadingElement) loadingElement.style.display = 'none';
      if (errorElement) errorElement.style.display = 'block';
    }
  }, [error]);

  // Failsafe timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (isInitializing) {
        console.warn('UnifiedDashboard - Initialization timeout, forcing completion');
        setIsInitializing(false);
        if (!roleInfo && user) {
          // Create a basic role info as fallback
          const { getExplicitUserRoles } = require('../../lib/roleUtils.ts');
          const fallbackRoles = getExplicitUserRoles(user);
          userRoleInfoStore.set(fallbackRoles);
          console.log('UnifiedDashboard - Fallback roles set:', fallbackRoles);
        }
      }
    }, 8000); // 8 second timeout (shorter than page timeout)

    return () => clearTimeout(timeout);
  }, [isInitializing, user, roleInfo]);

  // Additional safety check - if we have a user but no initialization after 3 seconds, force it
  useEffect(() => {
    if (user && isInitializing) {
      const quickTimeout = setTimeout(() => {
        console.log('UnifiedDashboard - Quick initialization check, forcing role setup');
        if (!roleInfo) {
          const { getExplicitUserRoles } = require('../../lib/roleUtils.ts');
          const quickRoles = getExplicitUserRoles(user);
          userRoleInfoStore.set(quickRoles);
          console.log('UnifiedDashboard - Quick roles set:', quickRoles);
        }
        setIsInitializing(false);
      }, 3000);

      return () => clearTimeout(quickTimeout);
    }
  }, [user, isInitializing, roleInfo]);

  // Initialize role switcher with user's primary role
  const { currentRole, switchRole, getPreferredRole } = useRoleSwitcher(
    roleInfo?.primaryRole || 'renter'
  );

  // State for dashboard view (can be a role or 'combined')
  const [currentView, setCurrentView] = useState<DashboardView>(currentRole);

  useEffect(() => {
    console.log('UnifiedDashboard - Effect triggered:', {
      hasUser: !!user,
      hasRoleInfo: !!roleInfo,
      roleDetectionLoading,
      userEmail: user?.email,
      userRoles: user?.roles,
      isInitializing
    });

    if (user && !roleInfo && !roleDetectionLoading) {
      console.log('UnifiedDashboard - Initializing roles for user:', user.email, 'Roles:', user.roles);
      initializeRoles();
    } else if (user && roleInfo) {
      console.log('UnifiedDashboard - User and roleInfo already available, skipping initialization');
      setIsInitializing(false);
    } else if (!user) {
      console.log('UnifiedDashboard - No user available, stopping initialization');
      setIsInitializing(false);
    }
  }, [user, roleInfo, roleDetectionLoading]);

  useEffect(() => {
    // Update current role when role info changes
    if (roleInfo && roleInfo.roles.length > 0) {
      const preferredRole = getPreferredRole(roleInfo.roles);
      if (preferredRole !== currentRole) {
        switchRole(preferredRole);
      }

      // Set initial view based on dashboard features
      const features = getDashboardFeatures(roleInfo);
      setCurrentView(features.defaultView);

      setIsInitializing(false);
    }
  }, [roleInfo]);

  const initializeRoles = async () => {
    if (!user) {
      console.error('UnifiedDashboard - No user provided to initializeRoles');
      return;
    }

    try {
      setError(null);
      console.log('UnifiedDashboard - Starting role detection for user:', user.email, 'User roles:', user.roles);

      // First, use quick role detection to get immediate results
      const { getExplicitUserRoles } = await import('../../lib/roleUtils.ts');
      const quickRoles = getExplicitUserRoles(user);
      console.log('UnifiedDashboard - Quick roles detected:', quickRoles);

      // Set quick roles immediately to unblock the UI
      userRoleInfoStore.set(quickRoles);
      const preferredRole = getPreferredRole(quickRoles.roles);
      console.log('UnifiedDashboard - Setting preferred role:', preferredRole);
      switchRole(preferredRole);
      setIsInitializing(false);

      // Then, enhance with full role detection in the background (with timeout)
      const enhanceRoles = async () => {
        try {
          console.log('UnifiedDashboard - Starting enhanced role detection...');
          const detectedRoles = await Promise.race([
            detectUserRoles(user),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Role detection timeout')), 10000)
            )
          ]) as UserRoleInfo;

          console.log('UnifiedDashboard - Enhanced roles detected:', detectedRoles);

          if (detectedRoles) {
            userRoleInfoStore.set(detectedRoles);
            const newPreferredRole = getPreferredRole(detectedRoles.roles);
            if (newPreferredRole !== preferredRole) {
              console.log('UnifiedDashboard - Updating preferred role to:', newPreferredRole);
              switchRole(newPreferredRole);
            }
          }
        } catch (enhancementError) {
          console.warn('UnifiedDashboard - Role enhancement failed, using quick roles:', enhancementError);
        }
      };

      // Run enhancement in background
      enhanceRoles();

    } catch (err) {
      console.error('Error initializing roles:', err);
      setError('Failed to initialize dashboard');
      setIsInitializing(false);
    }
  };

  // Loading state - be more specific about when to show loading
  const shouldShowLoading = (isInitializing && !!user) || roleDetectionLoading;

  if (shouldShowLoading) {
    console.log('UnifiedDashboard - Loading state:', {
      isInitializing,
      roleDetectionLoading,
      hasUser: !!user,
      hasRoleInfo: !!roleInfo,
      userEmail: user?.email,
      shouldShowLoading
    });
    return (
      <div className={`${className}`}>
        <div className="animate-pulse">
          {/* Header skeleton */}
          <div className="mb-8">
            <div className="h-8 bg-slate-200 rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-slate-200 rounded w-1/2"></div>
          </div>
          
          {/* Stats skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                <div className="h-4 bg-slate-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-slate-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
          
          {/* Content skeleton */}
          <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
            <div className="h-6 bg-slate-200 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-16 bg-slate-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // No user state - redirect to login
  if (!user) {
    console.log('UnifiedDashboard - No user found, redirecting to login');
    if (typeof globalThis !== 'undefined' && globalThis.location) {
      globalThis.location.href = '/auth/login';
    }
    return (
      <div className={`${className}`}>
        <div className="text-center py-12">
          <p className="text-slate-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-xl p-6">
          <div className="flex items-center">
            <div className="text-red-500 mr-3">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-red-800 font-medium">Dashboard Error</h3>
              <p className="text-red-600 text-sm mt-1">{error}</p>
            </div>
          </div>
          <button
            type="button"
            onClick={initializeRoles}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!roleInfo) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-12">
          <p className="text-slate-600">Unable to load dashboard. Please refresh the page.</p>
        </div>
      </div>
    );
  }

  const dashboardFeatures = getDashboardFeatures(roleInfo);

  console.log('UnifiedDashboard - Render state:', {
    currentView,
    currentRole,
    dashboardFeatures,
    roleInfo,
    showRenterFeatures: dashboardFeatures.showRenterFeatures,
    showOwnerFeatures: dashboardFeatures.showOwnerFeatures,
    showAdminFeatures: dashboardFeatures.showAdminFeatures
  });

  return (
    <div className={className}>
      {/* Role Switcher for dual-role users */}
      {dashboardFeatures.showRoleSwitcher && (
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-slate-900">Dashboard</h1>
              <p className="text-slate-600 text-sm mt-1">
                You have multiple roles. Switch between them to access different features.
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <RoleSwitcher
                availableRoles={roleInfo.roles}
                currentRole={currentRole}
                onRoleChange={(role) => {
                  switchRole(role);
                  setCurrentView(role);
                }}
              />
              <button
                type="button"
                onClick={() => setCurrentView(currentView === 'combined' ? currentRole : 'combined')}
                className="px-3 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
              >
                {currentView === 'combined' ? 'Single View' : 'Combined View'}
              </button>
            </div>
          </div>
        </div>
      )}



      {/* Role-specific dashboard content */}
      {currentView === 'admin' && dashboardFeatures.showAdminFeatures && (
        <AdminDashboard />
      )}

      {currentView === 'owner' && dashboardFeatures.showOwnerFeatures && (
        <OwnerDashboard />
      )}

      {currentView === 'renter' && dashboardFeatures.showRenterFeatures && (
        <RenterDashboard />
      )}

      {/* Combined view for dual-role users */}
      {currentView === 'combined' && (
        <div className="space-y-12">
          {/* Show dashboards in the order of user's roles array */}
          {roleInfo?.roles.map((role, index) => {
            if (role === 'owner' && dashboardFeatures.showOwnerFeatures) {
              return (
                <div key={`owner-${index}`}>
                  <div className="flex items-center mb-6">
                    <div className="p-2 bg-green-100 rounded-lg mr-3">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <h2 className="text-xl font-semibold text-slate-900">Your Venues</h2>
                  </div>
                  <OwnerDashboard />
                </div>
              );
            } else if (role === 'renter' && dashboardFeatures.showRenterFeatures) {
              return (
                <div key={`renter-${index}`}>
                  <div className="flex items-center mb-6">
                    <div className="p-2 bg-blue-100 rounded-lg mr-3">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <h2 className="text-xl font-semibold text-slate-900">Your Bookings</h2>
                  </div>
                  <RenterDashboard />
                </div>
              );
            } else if (role === 'admin' && dashboardFeatures.showAdminFeatures) {
              return (
                <div key={`admin-${index}`}>
                  <div className="flex items-center mb-6">
                    <div className="p-2 bg-purple-100 rounded-lg mr-3">
                      <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <h2 className="text-xl font-semibold text-slate-900">Administration</h2>
                  </div>
                  <AdminDashboard />
                </div>
              );
            }
            return null;
          })}
        </div>
      )}

      {/* Fallback for edge cases - show renter dashboard if nothing else matches */}
      {!dashboardFeatures.showRenterFeatures &&
       !dashboardFeatures.showOwnerFeatures &&
       !dashboardFeatures.showAdminFeatures && (
        <RenterDashboard />
      )}

      {/* Ultimate fallback if everything fails */}
      {!roleInfo && (
        <div className="text-center py-12">
          <div className="text-slate-400 text-lg mb-2">Welcome to Trodoo!</div>
          <p className="text-slate-600 mb-6">
            Get started by browsing venues or listing your own space.
          </p>
          <div className="space-x-3">
            <a
              href="/venues"
              className="inline-flex items-center px-6 py-3 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-colors"
            >
              Browse Venues
            </a>
            <a
              href="/venues/new"
              className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 transition-colors"
            >
              List Your Venue
            </a>
          </div>
        </div>
      )}
    </div>
  );
}
