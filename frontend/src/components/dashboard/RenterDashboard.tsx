import { useState, useEffect } from 'react';
import { useStore } from '@nanostores/react';
import { userStore } from '../../lib/state.ts';
import { getUserBookings } from '../../lib/pocketbase.ts';
import type { Booking } from '../../types/booking.ts';
import BookingList from './BookingList.tsx';
import { Calendar, Heart, Search, MapPin, Clock, Star } from 'lucide-react';

interface RenterDashboardProps {
  className?: string;
}

interface RenterStats {
  totalBookings: number;
  upcomingBookings: number;
  completedBookings: number;
  favoriteVenues: number;
  totalSpent: number;
}

export default function RenterDashboard({ className = '' }: RenterDashboardProps) {
  const user = useStore(userStore);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [stats, setStats] = useState<RenterStats>({
    totalBookings: 0,
    upcomingBookings: 0,
    completedBookings: 0,
    favoriteVenues: 0,
    totalSpent: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadRenterData();
    }
  }, [user]);

  const loadRenterData = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);

      console.log('RenterDashboard - Loading data for user:', user.id, user.email);

      // Load renter bookings
      const bookingsResult = await getUserBookings(user.id, 1, 20, 'renter');
      console.log('RenterDashboard - Bookings result:', bookingsResult);

      if (bookingsResult.success) {
        setBookings(bookingsResult.bookings);
        console.log('RenterDashboard - Bookings loaded:', bookingsResult.bookings.length, 'bookings');
        
        // Calculate stats
        const now = new Date();
        const totalBookings = bookingsResult.totalItems;
        const upcomingBookings = bookingsResult.bookings.filter(booking => {
          const startDate = new Date(booking.start_date);
          return startDate > now && ['confirmed', 'paid'].includes(booking.status);
        }).length;
        
        const completedBookings = bookingsResult.bookings.filter(booking => 
          booking.status === 'completed'
        ).length;
        
        const totalSpent = bookingsResult.bookings
          .filter(booking => ['completed', 'paid'].includes(booking.status))
          .reduce((sum, booking) => sum + booking.total_price, 0);

        setStats({
          totalBookings,
          upcomingBookings,
          completedBookings,
          favoriteVenues: 0, // TODO: Implement favorites
          totalSpent,
        });
      } else {
        setError(bookingsResult.error || 'Failed to load bookings');
      }
    } catch (err) {
      console.error('Error loading renter data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBookingAction = (bookingId: string, action: string) => {
    switch (action) {
      case 'view':
      case 'message':
        window.location.href = `/bookings/${bookingId}`;
        break;
      case 'pay':
        window.location.href = `/bookings/${bookingId}?action=pay`;
        break;
      default:
        window.location.href = `/bookings/${bookingId}`;
    }
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
              <div className="h-4 bg-slate-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-slate-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div className="h-6 bg-slate-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-slate-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-xl p-6 ${className}`}>
        <div className="flex items-center">
          <div className="text-red-500 mr-3">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="text-red-800 font-medium">Error Loading Dashboard</h3>
            <p className="text-red-600 text-sm mt-1">{error}</p>
          </div>
        </div>
        <button
          onClick={loadRenterData}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Welcome Section */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-slate-900 mb-2">
          Welcome back, {user?.name || 'Renter'}!
        </h2>
        <p className="text-slate-600">
          Manage your bookings and discover amazing venues for your events.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600">Total Bookings</p>
              <p className="text-2xl font-bold text-slate-900">{stats.totalBookings}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Calendar className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600">Upcoming</p>
              <p className="text-2xl font-bold text-slate-900">{stats.upcomingBookings}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <Clock className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600">Completed</p>
              <p className="text-2xl font-bold text-slate-900">{stats.completedBookings}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <Star className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600">Total Spent</p>
              <p className="text-2xl font-bold text-slate-900">
                ₦{stats.totalSpent.toLocaleString()}
              </p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Bookings */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl shadow-sm border border-slate-200">
            <div className="px-6 py-4 border-b border-slate-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-slate-900">Recent Bookings</h3>
                <a
                  href="/bookings"
                  className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                >
                  View All
                </a>
              </div>
            </div>
            <div className="p-6">
              {bookings.length > 0 ? (
                <BookingList
                  bookings={bookings.slice(0, 5)}
                  userRole="renter"
                  onBookingAction={handleBookingAction}
                  isLoading={false}
                />
              ) : (
                <div className="text-center py-8">
                  <Calendar className="w-12 h-12 text-slate-300 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-slate-900 mb-2">No bookings yet</h4>
                  <p className="text-slate-600 mb-4">
                    Start exploring venues to make your first booking.
                  </p>
                  <a
                    href="/venues"
                    className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    <Search className="w-4 h-4 mr-2" />
                    Browse Venues
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-slate-200">
            <div className="px-6 py-4 border-b border-slate-200">
              <h3 className="text-lg font-semibold text-slate-900">Quick Actions</h3>
            </div>
            <div className="p-6 space-y-3">
              <a
                href="/venues"
                className="flex items-center p-3 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors group"
              >
                <div className="p-2 bg-primary-100 rounded-lg group-hover:bg-primary-200 transition-colors">
                  <Search className="w-5 h-5 text-primary-600" />
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-slate-900">Browse Venues</h4>
                  <p className="text-xs text-slate-600">Find the perfect space</p>
                </div>
              </a>

              <a
                href="/favorites"
                className="flex items-center p-3 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors group"
              >
                <div className="p-2 bg-red-100 rounded-lg group-hover:bg-red-200 transition-colors">
                  <Heart className="w-5 h-5 text-red-600" />
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-slate-900">My Favorites</h4>
                  <p className="text-xs text-slate-600">Saved venues</p>
                </div>
              </a>

              <a
                href="/dashboard/profile"
                className="flex items-center p-3 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors group"
              >
                <div className="p-2 bg-slate-100 rounded-lg group-hover:bg-slate-200 transition-colors">
                  <svg className="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-slate-900">Profile Settings</h4>
                  <p className="text-xs text-slate-600">Update your info</p>
                </div>
              </a>
            </div>
          </div>

          {/* Recommendations */}
          <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-xl p-6 border border-primary-200">
            <div className="flex items-center mb-3">
              <MapPin className="w-5 h-5 text-primary-600 mr-2" />
              <h3 className="text-lg font-semibold text-primary-900">Discover New Venues</h3>
            </div>
            <p className="text-primary-700 text-sm mb-4">
              Explore trending venues in your area and find the perfect space for your next event.
            </p>
            <a
              href="/venues?featured=true"
              className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors text-sm font-medium"
            >
              Explore Featured
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
